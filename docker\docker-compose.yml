name: fodmap-docker-compose

services:
  db:
    container_name: fodmap-db-container
    image: postgres:15-alpine
    env_file: .env
    environment:
      # Pass additional environment variables for the init script
      - APP_DB_USER=${APP_DB_USER}
      - APP_DB_PASSWORD=${APP_DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../migrations/init.sh:/docker-entrypoint-initdb.d/init.sh
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data: